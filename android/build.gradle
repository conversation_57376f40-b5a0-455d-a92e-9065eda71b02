plugins {
    id "com.android.application" version "8.7.3" apply false
    id "com.android.library" version "8.7.3" apply false
    id "org.jetbrains.kotlin.android" version "2.1.0" apply false
    // START: FlutterFire Configuration
    id "com.google.gms.google-services" version "4.4.2" apply false
    id "com.google.firebase.crashlytics" version "2.8.1" apply false
    // END: FlutterFire Configuration
    id "dev.flutter.flutter-gradle-plugin" version "1.0.0" apply false
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
