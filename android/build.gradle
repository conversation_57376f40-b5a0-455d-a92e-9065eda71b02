plugins {
    id "com.android.application" version "8.1.4" apply false
    id "com.android.library" version "8.1.4" apply false
    id "org.jetbrains.kotlin.android" version "2.1.0" apply false
    // START: FlutterFire Configuration
    id "com.google.gms.google-services" version "4.4.2" apply false
    id "com.google.firebase.crashlytics" version "2.8.1" apply false
    // END: FlutterFire Configuration
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
