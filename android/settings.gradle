pluginManagement {
    def localPropertiesFile = new File(rootProject.projectDir, "local.properties")
    def properties = new Properties()

    assert localPropertiesFile.exists()
    localPropertiesFile.withReader("UTF-8") { reader -> properties.load(reader) }

    def flutterSdkPath = properties.getProperty("flutter.sdk")
    assert flutterSdkPath != null, "flutter.sdk not set in local.properties"

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
}

include ':app'

// dependencyResolutionManagement{
//     repositories{
//            maven { url "https://jitpack.io" }
//     }
// }
